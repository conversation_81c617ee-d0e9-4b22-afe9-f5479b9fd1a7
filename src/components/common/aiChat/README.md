# 通用AI聊天组件

## 概述

这是一个从现有ChatPreview组件抽取出来的通用AI聊天组件，支持应用、工作流以及未来可能的其他聊天场景。

## 特性

- ✅ 支持多种场景配置（应用、工作流等）
- ✅ 可自定义UI布局（居中布局 vs 固定输入框布局）
- ✅ 支持自定义API调用方式
- ✅ 支持自定义数据转换逻辑
- ✅ 支持语音合成和语音输入（可配置）
- ✅ 支持自定义头像和主题色
- ✅ 完全向后兼容，不影响现有功能

## 使用方式

### 基本用法

```tsx
import AIChat from '@/components/common/aiChat';
import { AIChatConfig, AIChatData, AIChatAPI } from '@/components/common/aiChat/types';

const config: AIChatConfig = {
  id: 'my-chat',
  name: '我的聊天助手',
  openingStatement: '欢迎使用聊天助手！',
  layout: {
    fixedInput: false, // 应用场景：false，工作流场景：true
    centeredWelcome: true,
  },
};

const data: AIChatData = {
  getCompleteData: () => myData,
};

const api: AIChatAPI = {
  chatRequest: myChatRequest,
  buildChatParams: (input, data) => ({ input, ...data }),
};

<AIChat config={config} data={data} api={api} />
```

### 应用场景配置

```tsx
const appConfig: AIChatConfig = {
  id: appData.id,
  name: appData.name,
  openingStatement: appData.openingStatement,
  textToSpeech: appData.textToSpeech,
  enableVoiceInput: appData.speechToText,
  layout: {
    fixedInput: false, // 输入框和欢迎界面都垂直居中
    centeredWelcome: true,
  },
  avatar: <AppAvatar />,
  color: getColorFromString(appId),
};
```

### 工作流场景配置

```tsx
const workflowConfig: AIChatConfig = {
  id: workflowData.id,
  name: workflowData.label,
  openingStatement: '欢迎使用工作流调试功能',
  textToSpeech: false,
  enableVoiceInput: false,
  layout: {
    fixedInput: true, // 输入框固定在底部
    centeredWelcome: true, // 欢迎界面垂直居中
  },
  avatar: <WorkflowAvatar />,
  hideHeader: true, // 使用自定义头部
};
```

## API接口

### AIChatConfig

聊天组件的基础配置

- `id`: 聊天实例ID
- `name`: 聊天助手名称
- `description`: 描述信息
- `openingStatement`: 欢迎语
- `textToSpeech`: 是否启用语音合成
- `enableVoiceInput`: 是否启用语音输入
- `avatar`: 自定义头像组件
- `color`: 主题色
- `hideHeader`: 是否隐藏头部
- `customTitle`: 自定义标题
- `layout`: 布局配置
  - `fixedInput`: 输入框是否固定在底部
  - `centeredWelcome`: 欢迎界面是否垂直居中

### AIChatData

聊天数据配置

- `getCompleteData`: 获取完整数据的方法
- `conversation`: 历史对话数据
- `conversationId`: 对话ID

### AIChatAPI

API调用配置

- `chatRequest`: 聊天请求对象（包含mutate和abort方法）
- `transformData`: 数据转换方法
- `transformConversation`: 对话数据转换方法
- `buildChatParams`: 构建请求参数的方法

## 布局差异

### 应用模式 (fixedInput: false)
- 欢迎界面和输入框都垂直居中展示
- 适用于应用聊天场景

### 工作流模式 (fixedInput: true)
- 欢迎界面垂直居中展示
- 输入框固定在底部位置
- 适用于工作流调试场景

## 兼容性

- ✅ 完全向后兼容现有ChatPreview组件
- ✅ 不影响应用模块的任何现有功能
- ✅ 支持TypeScript类型检查
- ✅ 支持所有现有的聊天功能（消息渲染、滚动控制、状态管理等）

## 文件结构

```
src/components/common/aiChat/
├── index.tsx          # 主组件
├── types.ts           # 类型定义
└── README.md          # 文档
```

## 相关文件

- `src/controllers/API/queries/workflow/useWorkflowChat.ts` - 工作流聊天API Hook
- `src/pages/workflowPage/components/workbench/debugPanel/index.tsx` - 工作流调试面板集成
- `src/pages/AppPage/components/chatPreview/index.tsx` - 原始应用聊天组件（未修改）
