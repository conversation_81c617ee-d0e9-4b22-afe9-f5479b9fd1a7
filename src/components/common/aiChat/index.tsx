import { useEffect, useRef, useState } from "react";

import Chat, { IContentRef } from "@/components/antd/chat";
import { MessageStatus } from "@/components/antd/chat/entity";
import { Button } from "@/components/main/button";
import { DownArrowIcon, EraseIcon, PauseIcon } from "@/components/main/icon";
import { useShowScrollToBottom } from "@/hooks/common/useShowScrollToBottom";
import ChatInput from "@/pages/AppPage/components/chatInput";
import { cn } from "@/utils/utils";
import { AIChatProps } from "./types";

import "./index.css";

function AIChat({
  config,
  data,
  api,
  callbacks,
  messageIconRenderer,
  isDebugger = false,
}: AIChatProps) {
  const [value, setValue] = useState<string | undefined>();

  const containerRef = useRef<IContentRef>(null);
  const chat = Chat.useChat();
  const showScrollToBottom = useShowScrollToBottom(
    containerRef.current?.nativeElement,
  );

  const chatData = chat.conversation.get()?.prompts || [];

  useEffect(() => {
    if (chat.conversation) {
      chat.conversation.remove();
    }

    const conversationId = `conversation_${new Date().valueOf()}`;
    chat.conversation.create({ id: conversationId });

    if (data.conversation && api.transformConversation) {
      const transformedData = api.transformConversation(data.conversation);
      chat.conversation.update({ assistantId: data.conversation.id });

      transformedData.forEach((item: any) => {
        chat.prompt.create({
          id: item.id,
          title: item.title,
          assistantId: item.assistantId,
        });
        item.answers.forEach((answer: any) => {
          chat.message.create(item.id, {
            id: answer.id,
            content: answer.content,
            assistantId: answer.assistantId as any,
            status: MessageStatus.DONE,
          });
        });
      });
    }
  }, [data.conversation, api.transformConversation]);

  const handleSubmit = (raw = value) => {
    const val = raw?.trim();
    const completeData = data.getCompleteData();
    if (!completeData || chat.loading() || !val) return;

    setValue("");
    const promptId = new Date().valueOf().toString();
    const messageId = (new Date().valueOf() + 1).toString();
    chat.prompt.create({ id: promptId, title: val });
    chat.message.create(promptId, { id: messageId, content: "" });

    const requestParams = api.buildChatParams(val, completeData);

    api.chatRequest.mutate(requestParams, {
      onopen() {
        chat.start(promptId, messageId);
      },
      onmessage(data) {
        const conversationId = chat.conversation.get()?.assistantId;

        if (!conversationId && data.conversation_id) {
          chat.conversation.update({ assistantId: data.conversation_id });
        }
        chat.push(promptId, messageId, data.content);
      },
      onclose(data) {
        chat.close(promptId, messageId);
        if (!isDebugger && data?.conversation_id && !data.conversationId) {
          callbacks?.onCreateConversation?.(data.conversation_id);
        }
      },
    });
  };

  const handleClearChat = () => {
    chat.conversation.remove();
    chat.conversation.create({ id: new Date().valueOf().toString() });
    callbacks?.onClearChat?.();
  };

  const handleStop = () => {
    const prompts = chat.conversation.get()?.prompts;
    const lastPrompt = prompts?.[prompts.length - 1];
    const lastMessage = lastPrompt?.messages?.[lastPrompt.messages.length - 1];
    if (!lastMessage) return;

    if (
      lastMessage.status === MessageStatus.PENDING ||
      lastMessage.status === MessageStatus.GENERATING
    ) {
      api.chatRequest.abort();
      chat.message.update(lastPrompt.id, lastMessage.id, {
        status: MessageStatus.STOPPED,
      });
    }
    callbacks?.onStop?.();
  };

  return (
    <div className={cn("flex h-full flex-1 flex-col", "ai-chat-container")}>
      {!config.hideHeader && (
        <div className="border-b-solid flex h-[46px] items-center justify-between border-b border-border-1 bg-bg-light-3 px-10 py-3 text-sm font-medium">
          <div>{config.customTitle || "调试预览"}</div>
          <EraseIcon
            className="cursor-pointer text-base text-text-2"
            onClick={handleClearChat}
          />
        </div>
      )}
      {config.layout?.fixedInput ? (
        // 工作流布局：输入框固定在底部
        <>
          <div className="flex min-h-0 flex-1 flex-col">
            <div
              className={cn(
                "relative min-h-0 flex-1 overflow-y-auto",
                chatData.length ? "" : "hidden",
              )}
            >
              <Chat
                chat={chat}
                regenerate={false}
                messageIcons={messageIconRenderer}
              >
                <Chat.Content
                  ref={containerRef}
                  data={chatData}
                  avatar={config.avatar}
                />
              </Chat>
              {showScrollToBottom && (
                <Button
                  className="absolute bottom-2 right-1/2 z-10 translate-x-1/2"
                  variant="outline"
                  size="iconSm"
                  onClick={() => containerRef.current?.scrollToBottom()}
                >
                  <DownArrowIcon />
                </Button>
              )}
            </div>
            {!chatData.length && (
              <div className="flex flex-1 flex-col items-center justify-center gap-6 p-3">
                <div className="flex flex-col items-center justify-center gap-2">
                  <div className="text-[60px] leading-[60px]">✨</div>
                  <div className="text- text-xl font-medium">{config.name}</div>
                </div>
                <div className="max-w-[480px] text-sm text-text-2">
                  {config.openingStatement}
                </div>
              </div>
            )}
          </div>
          <div className="flex flex-col items-center p-3">
            <div
              className={cn(
                "w-full",
                chatData.length ? "max-w-[800px]" : "max-w-[592px]",
              )}
            >
              <ChatInput
                value={value}
                onChange={setValue}
                onPressEnter={() => handleSubmit()}
                button={{
                  disabled: chat.loading() || !value?.trim(),
                }}
                placeholder="Ask anything"
                onSubmit={handleSubmit}
                generating={chat.loading()}
                onStop={handleStop}
                enableVoiceInput={config.enableVoiceInput}
              />
            </div>
          </div>
        </>
      ) : (
        // 应用布局：欢迎界面和输入框都垂直居中
        <div className="flex min-h-0 flex-1 flex-col justify-center">
          <div
            className={cn(
              "relative min-h-0 flex-1 overflow-y-auto",
              chatData.length ? "" : "hidden",
            )}
          >
            <Chat
              chat={chat}
              regenerate={false}
              messageIcons={messageIconRenderer}
            >
              <Chat.Content
                ref={containerRef}
                data={chatData}
                avatar={config.avatar}
              />
            </Chat>
            {showScrollToBottom && (
              <Button
                className="absolute bottom-2 right-1/2 z-10 translate-x-1/2"
                variant="outline"
                size="iconSm"
                onClick={() => containerRef.current?.scrollToBottom()}
              >
                <DownArrowIcon />
              </Button>
            )}
          </div>
          <div className="flex flex-col items-center justify-center gap-6 px-[40px] py-3">
            {chatData.length ? null : (
              <>
                <div className="flex flex-col items-center justify-center gap-2">
                  <div className="text-[60px] leading-[60px]">✨</div>
                  <div className="text- text-xl font-medium">{config.name}</div>
                </div>
                <div className="max-w-[480px] text-sm text-text-2">
                  {config.openingStatement}
                </div>
              </>
            )}
            <div
              className={cn(
                "w-full",
                chatData.length ? "max-w-[800px]" : "max-w-[592px]",
              )}
            >
              <ChatInput
                value={value}
                onChange={setValue}
                onPressEnter={() => handleSubmit()}
                button={{
                  disabled: chat.loading() || !value?.trim(),
                }}
                placeholder="Ask anything"
                onSubmit={handleSubmit}
                generating={chat.loading()}
                onStop={handleStop}
                enableVoiceInput={config.enableVoiceInput}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default AIChat;
export * from "./types";
