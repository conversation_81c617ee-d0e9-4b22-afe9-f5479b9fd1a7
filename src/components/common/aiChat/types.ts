import { ReactNode } from "react";
import { Message } from "@/components/antd/chat/entity";

// 通用聊天配置接口
export interface AIChatConfig {
  // 基础信息
  id: string;
  name?: string;
  description?: string;
  openingStatement?: string;
  
  // 功能开关
  textToSpeech?: boolean;
  enableVoiceInput?: boolean;
  
  // UI配置
  avatar?: ReactNode;
  color?: string;
  hideHeader?: boolean;
  customTitle?: string;
  
  // 布局配置
  layout?: {
    // 输入框是否固定在底部（工作流场景）
    fixedInput?: boolean;
    // 欢迎界面是否垂直居中
    centeredWelcome?: boolean;
  };
}

// 聊天数据接口
export interface AIChatData {
  // 获取完整数据的方法
  getCompleteData: () => any;
  // 历史对话数据
  conversation?: any;
  conversationId?: string;
}

// API配置接口
export interface AIChatAPI {
  // 聊天请求方法
  chatRequest: {
    mutate: (
      params: Record<string, any>,
      listeners: {
        onopen: () => void;
        onmessage: (data: any) => void;
        onclose: (data?: any) => void;
      }
    ) => void;
    abort: () => void;
  };
  
  // 数据转换方法
  transformData?: (data: any) => any;
  transformConversation?: (conversation: any) => any;
  
  // 构建请求参数的方法
  buildChatParams: (input: string, completeData: any) => Record<string, any>;
}

// 事件回调接口
export interface AIChatCallbacks {
  onCreateConversation?: (conversationId: string) => void;
  onClearChat?: () => void;
  onStop?: () => void;
}

// 消息图标渲染函数类型
export type MessageIconRenderer = (record?: Message) => ReactNode;

// 通用AI聊天组件Props
export interface AIChatProps {
  config: AIChatConfig;
  data: AIChatData;
  api: AIChatAPI;
  callbacks?: AIChatCallbacks;
  messageIconRenderer?: MessageIconRenderer;
  isDebugger?: boolean;
}
