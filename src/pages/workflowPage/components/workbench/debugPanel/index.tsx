import { Button } from "antd";

import AI<PERSON>hat from "@/components/common/aiChat";
import {
  AIChatAPI,
  AIChatCallbacks,
  AIChatConfig,
  AIChatData,
} from "@/components/common/aiChat/types";
import { CloseIcon, EraseIcon, RepairIcon } from "@/components/main/icon";
import {
  buildWorkflowChatParams,
  transformWorkflowData,
  useWorkflowChat,
} from "@/controllers/API/queries/workflow/useWorkflowChat";
import { useWorkflowStore } from "@/stores/workflowStore";
import { cn } from "@/utils/utils";
import ResizablePanel from "../../resizablePanel";

export default function DebugPanel() {
  const debugPanel = useWorkflowStore((state) => state.debugPanel);
  const setDebugPanel = useWorkflowStore((state) => state.setDebugPanel);
  const currentWorkflow = useWorkflowStore((state) => state.currentWorkflow);

  const workflowChatRequest = useWorkflowChat();

  if (!debugPanel.isOpen) return null;

  // 配置聊天组件
  const chatConfig: AIChatConfig = {
    id: currentWorkflow?.id || "workflow-debug",
    openingStatement: "在下面的输入框中输入内容开始调试",
    textToSpeech: false, // 工作流场景暂不支持TTS
    enableVoiceInput: false, // 工作流场景暂不支持语音输入
    hideHeader: true, // 使用自定义头部
    layout: {
      fixedInput: true, // 输入框固定在底部
      centeredWelcome: true, // 欢迎界面垂直居中
    },
    avatar: (
      <div
        className={cn(
          "flex h-6 w-6 items-center justify-center rounded-sm bg-bg-primary-1 text-sm leading-4",
        )}
      >
        ✨
      </div>
    ),
  };

  const chatData: AIChatData = {
    getCompleteData: () => currentWorkflow,
    conversation: undefined, // 工作流调试不需要历史对话
    conversationId: undefined,
  };

  const chatAPI: AIChatAPI = {
    chatRequest: workflowChatRequest,
    buildChatParams: (input: string, workflowData: unknown) =>
      buildWorkflowChatParams(input, workflowData),
    transformData: transformWorkflowData,
  };

  const chatCallbacks: AIChatCallbacks = {
    onClearChat: () => {
      console.log("清除工作流调试聊天记录");
    },
    onStop: () => {
      console.log("停止工作流调试");
    },
  };

  // 消息图标渲染器（工作流场景暂不需要）
  const renderMessageIcons = () => {
    return null;
  };

  const handleClearChat = () => {
    console.log("清除工作流调试聊天记录");
  };

  return (
    <ResizablePanel
      width={debugPanel.width}
      onWidthChange={(width) => setDebugPanel({ width })}
    >
      <div className="flex h-full flex-col">
        <div className="flex h-[46px] items-center justify-between border-b border-border-1 bg-bg-light-3 p-3">
          <div className="flex items-center gap-2">
            <div className="text-sm font-medium">调试预览</div>
            <RepairIcon className="cursor-pointer text-base text-text-2" />
            <EraseIcon
              className="cursor-pointer text-base text-text-2"
              onClick={handleClearChat}
            />
          </div>
          <Button
            type="text"
            size="small"
            onClick={() => setDebugPanel({ isOpen: false })}
          >
            <CloseIcon />
          </Button>
        </div>
        <div className="min-h-0 flex-1">
          <AIChat
            config={chatConfig}
            data={chatData}
            api={chatAPI}
            callbacks={chatCallbacks}
            messageIconRenderer={renderMessageIcons}
            isDebugger={true}
          />
        </div>
      </div>
    </ResizablePanel>
  );
}
