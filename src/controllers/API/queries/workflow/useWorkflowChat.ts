import { useRef } from "react";

import { sse } from "../../api";

export type WorkflowActionType = "message" | "message_end";

export interface WorkflowMessageData {
  event: WorkflowActionType;
  content: string;
  conversation_id: string;
  created_at: string;
}

function parseJsonFromDataString(str: string) {
  try {
    const match = str.match(/^data:\s*(\{.*\})\n$/);
    if (!match) return null;

    const jsonStr = match[1];
    return JSON.parse(jsonStr);
  } catch (err) {
    console.error("JSON解析失败：", err);
    return null;
  }
}

export function useWorkflowChat() {
  const abortController = useRef<AbortController | undefined>(undefined);

  function mutate(
    params: Record<string, any>,
    listeners: {
      onopen: () => void;
      onmessage: (data: WorkflowMessageData) => void;
      onclose: (data?: WorkflowMessageData) => void;
    },
  ) {
    // 使用工作流调试API端点
    const url = `/api/v3/canvas/debug`;
    abortController.current = new AbortController();
    
    try {
      sse(url, params, {
        signal: abortController.current.signal,
        openWhenHidden: true,
        async onopen() {
          listeners.onopen?.();
        },
        onmessage(ev) {
          const data: WorkflowMessageData = parseJsonFromDataString(ev.data);
          if (!data) return;
          if (data.event === "message_end") {
            listeners.onclose?.(data);
            return;
          }
          listeners.onmessage?.(data);
        },
        onclose() {
          listeners.onclose?.();
        },
      });
    } catch (error) {
      console.error("工作流聊天请求失败:", error);
    }
  }

  function abort() {
    abortController.current?.abort();
  }

  return { mutate, abort };
}

// 工作流数据转换工具函数
export function transformWorkflowData(workflowData: any) {
  return {
    id: workflowData.id,
    name: workflowData.label,
    description: workflowData.description,
    nodes: workflowData.nodes,
    edges: workflowData.edges,
  };
}

// 构建工作流聊天请求参数
export function buildWorkflowChatParams(
  input: string,
  workflowData: any,
  conversationId?: string
): Record<string, any> {
  return {
    conversation_id: conversationId,
    input_value: input,
    workflow_id: workflowData.id,
    nodes: workflowData.nodes,
    edges: workflowData.edges,
    from_source: "debugger",
  };
}
